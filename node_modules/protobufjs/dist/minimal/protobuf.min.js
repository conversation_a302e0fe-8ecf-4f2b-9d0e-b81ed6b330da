/*!
 * protobuf.js v6.11.3 (c) 2016, daniel wirtz
 * compiled fri, 11 aug 2023 21:14:29 utc
 * licensed under the bsd-3-clause license
 * see: https://github.com/dcodeio/protobuf.js for details
 */
!function(g){"use strict";!function(r,u,t){var n=function t(n){var i=u[n];return i||r[n][0].call(i=u[n]={exports:{}},t,i,i.exports),i.exports}(t[0]);n.util.global.protobuf=n,"function"==typeof define&&define.amd&&define(["long"],function(t){return t&&t.isLong&&(n.util.Long=t,n.configure()),n}),"object"==typeof module&&module&&module.exports&&(module.exports=n)}({1:[function(t,n,i){n.exports=function(t,n){var i=Array(arguments.length-1),s=0,r=2,e=!0;for(;r<arguments.length;)i[s++]=arguments[r++];return new Promise(function(r,u){i[s]=function(t){if(e)if(e=!1,t)u(t);else{for(var n=Array(arguments.length-1),i=0;i<n.length;)n[i++]=arguments[i];r.apply(null,n)}};try{t.apply(n||null,i)}catch(t){e&&(e=!1,u(t))}})}},{}],2:[function(t,n,i){i.length=function(t){var n=t.length;if(!n)return 0;for(var i=0;1<--n%4&&"="==(t[0|n]||"");)++i;return Math.ceil(3*t.length)/4-i};for(var o=Array(64),f=Array(123),r=0;r<64;)f[o[r]=r<26?r+65:r<52?r+71:r<62?r-4:r-59|43]=r++;i.encode=function(t,n,i){for(var r,u=null,s=[],e=0,h=0;n<i;){var f=t[n++];switch(h){case 0:s[e++]=o[f>>2],r=(3&f)<<4,h=1;break;case 1:s[e++]=o[r|f>>4],r=(15&f)<<2,h=2;break;case 2:s[e++]=o[r|f>>6],s[e++]=o[63&f],h=0}8191<e&&((u=u||[]).push(String.fromCharCode.apply(String,s)),e=0)}return h&&(s[e++]=o[r],s[e++]=61,1===h&&(s[e++]=61)),u?(e&&u.push(String.fromCharCode.apply(String,s.slice(0,e))),u.join("")):String.fromCharCode.apply(String,s.slice(0,e))};var c="invalid encoding";i.decode=function(t,n,i){for(var r,u=i,s=0,e=0;e<t.length;){var h=t.charCodeAt(e++);if(61==h&&1<s)break;if((h=f[h])===g)throw Error(c);switch(s){case 0:r=h,s=1;break;case 1:n[i++]=r<<2|(48&h)>>4,r=h,s=2;break;case 2:n[i++]=(15&r)<<4|(60&h)>>2,r=h,s=3;break;case 3:n[i++]=(3&r)<<6|h,s=0}}if(1===s)throw Error(c);return i-u},i.test=function(t){return/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(t)}},{}],3:[function(t,n,i){function r(){this.t={}}(n.exports=r).prototype.on=function(t,n,i){return(this.t[t]||(this.t[t]=[])).push({fn:n,ctx:i||this}),this},r.prototype.off=function(t,n){if(t===g)this.t={};else if(n===g)this.t[t]=[];else for(var i=this.t[t],r=0;r<i.length;)i[r].fn===n?i.splice(r,1):++r;return this},r.prototype.emit=function(t){var n=this.t[t];if(n){for(var i=[],r=1;r<arguments.length;)i.push(arguments[r++]);for(r=0;r<n.length;)n[r].fn.apply(n[r++].ctx,i)}return this}},{}],4:[function(t,n,i){function r(t){function n(t,n,i,r){var u=n<0?1:0;t(0===(n=u?-n:n)?0<1/n?0:2147483648:isNaN(n)?2143289344:34028234663852886e22<n?(u<<31|2139095040)>>>0:n<11754943508222875e-54?(u<<31|Math.round(n/1401298464324817e-60))>>>0:(u<<31|127+(t=Math.floor(Math.log(n)/Math.LN2))<<23|8388607&Math.round(n*Math.pow(2,-t)*8388608))>>>0,i,r)}function i(t,n,i){t=t(n,i),n=2*(t>>31)+1,i=t>>>23&255,t&=8388607;return 255==i?t?NaN:1/0*n:0==i?1401298464324817e-60*n*t:n*Math.pow(2,i-150)*(8388608+t)}function r(t,n,i){h[0]=t,n[i]=f[0],n[i+1]=f[1],n[i+2]=f[2],n[i+3]=f[3]}function u(t,n,i){h[0]=t,n[i]=f[3],n[i+1]=f[2],n[i+2]=f[1],n[i+3]=f[0]}function s(t,n){return f[0]=t[n],f[1]=t[n+1],f[2]=t[n+2],f[3]=t[n+3],h[0]}function e(t,n){return f[3]=t[n],f[2]=t[n+1],f[1]=t[n+2],f[0]=t[n+3],h[0]}var h,f,o,c,a;function l(t,n,i,r,u,s){var e,h=r<0?1:0;0===(r=h?-r:r)?(t(0,u,s+n),t(0<1/r?0:2147483648,u,s+i)):isNaN(r)?(t(0,u,s+n),t(2146959360,u,s+i)):17976931348623157e292<r?(t(0,u,s+n),t((h<<31|2146435072)>>>0,u,s+i)):r<22250738585072014e-324?(t((e=r/5e-324)>>>0,u,s+n),t((h<<31|e/4294967296)>>>0,u,s+i)):(t(4503599627370496*(e=r*Math.pow(2,-(r=1024===(r=Math.floor(Math.log(r)/Math.LN2))?1023:r)))>>>0,u,s+n),t((h<<31|r+1023<<20|1048576*e&1048575)>>>0,u,s+i))}function v(t,n,i,r,u){n=t(r,u+n),t=t(r,u+i),r=2*(t>>31)+1,u=t>>>20&2047,i=4294967296*(1048575&t)+n;return 2047==u?i?NaN:1/0*r:0==u?5e-324*r*i:r*Math.pow(2,u-1075)*(i+4503599627370496)}function w(t,n,i){o[0]=t,n[i]=c[0],n[i+1]=c[1],n[i+2]=c[2],n[i+3]=c[3],n[i+4]=c[4],n[i+5]=c[5],n[i+6]=c[6],n[i+7]=c[7]}function y(t,n,i){o[0]=t,n[i]=c[7],n[i+1]=c[6],n[i+2]=c[5],n[i+3]=c[4],n[i+4]=c[3],n[i+5]=c[2],n[i+6]=c[1],n[i+7]=c[0]}function b(t,n){return c[0]=t[n],c[1]=t[n+1],c[2]=t[n+2],c[3]=t[n+3],c[4]=t[n+4],c[5]=t[n+5],c[6]=t[n+6],c[7]=t[n+7],o[0]}function d(t,n){return c[7]=t[n],c[6]=t[n+1],c[5]=t[n+2],c[4]=t[n+3],c[3]=t[n+4],c[2]=t[n+5],c[1]=t[n+6],c[0]=t[n+7],o[0]}return"undefined"!=typeof Float32Array?(h=new Float32Array([-0]),f=new Uint8Array(h.buffer),a=128===f[3],t.writeFloatLE=a?r:u,t.writeFloatBE=a?u:r,t.readFloatLE=a?s:e,t.readFloatBE=a?e:s):(t.writeFloatLE=n.bind(null,g),t.writeFloatBE=n.bind(null,A),t.readFloatLE=i.bind(null,p),t.readFloatBE=i.bind(null,m)),"undefined"!=typeof Float64Array?(o=new Float64Array([-0]),c=new Uint8Array(o.buffer),a=128===c[7],t.writeDoubleLE=a?w:y,t.writeDoubleBE=a?y:w,t.readDoubleLE=a?b:d,t.readDoubleBE=a?d:b):(t.writeDoubleLE=l.bind(null,g,0,4),t.writeDoubleBE=l.bind(null,A,4,0),t.readDoubleLE=v.bind(null,p,0,4),t.readDoubleBE=v.bind(null,m,4,0)),t}function g(t,n,i){n[i]=255&t,n[i+1]=t>>>8&255,n[i+2]=t>>>16&255,n[i+3]=t>>>24}function A(t,n,i){n[i]=t>>>24,n[i+1]=t>>>16&255,n[i+2]=t>>>8&255,n[i+3]=255&t}function p(t,n){return(t[n]|t[n+1]<<8|t[n+2]<<16|t[n+3]<<24)>>>0}function m(t,n){return(t[n]<<24|t[n+1]<<16|t[n+2]<<8|t[n+3])>>>0}n.exports=r(r)},{}],5:[function(t,n,i){function r(t){try{var n=eval("require")(t);if(n&&(n.length||Object.keys(n).length))return n}catch(t){}return null}n.exports=r},{}],6:[function(t,n,i){n.exports=function(n,i,t){var r=t||8192,u=r>>>1,s=null,e=r;return function(t){if(t<1||u<t)return n(t);r<e+t&&(s=n(r),e=0);t=i.call(s,e,e+=t);return 7&e&&(e=1+(7|e)),t}}},{}],7:[function(t,n,i){i.length=function(t){for(var n,i=0,r=0;r<t.length;++r)(n=t.charCodeAt(r))<128?i+=1:n<2048?i+=2:55296==(64512&n)&&56320==(64512&t.charCodeAt(r+1))?(++r,i+=4):i+=3;return i},i.read=function(t,n,i){if(i-n<1)return"";for(var r,u=null,s=[],e=0;n<i;)(r=t[n++])<128?s[e++]=r:191<r&&r<224?s[e++]=(31&r)<<6|63&t[n++]:239<r&&r<365?(r=((7&r)<<18|(63&t[n++])<<12|(63&t[n++])<<6|63&t[n++])-65536,s[e++]=55296+(r>>10),s[e++]=56320+(1023&r)):s[e++]=(15&r)<<12|(63&t[n++])<<6|63&t[n++],8191<e&&((u=u||[]).push(String.fromCharCode.apply(String,s)),e=0);return u?(e&&u.push(String.fromCharCode.apply(String,s.slice(0,e))),u.join("")):String.fromCharCode.apply(String,s.slice(0,e))},i.write=function(t,n,i){for(var r,u,s=i,e=0;e<t.length;++e)(r=t.charCodeAt(e))<128?n[i++]=r:(r<2048?n[i++]=r>>6|192:(55296==(64512&r)&&56320==(64512&(u=t.charCodeAt(e+1)))?(++e,n[i++]=(r=65536+((1023&r)<<10)+(1023&u))>>18|240,n[i++]=r>>12&63|128):n[i++]=r>>12|224,n[i++]=r>>6&63|128),n[i++]=63&r|128);return i-s}},{}],8:[function(t,n,i){var r=i;function u(){r.util.n(),r.Writer.n(r.BufferWriter),r.Reader.n(r.BufferReader)}r.build="minimal",r.Writer=t(16),r.BufferWriter=t(17),r.Reader=t(9),r.BufferReader=t(10),r.util=t(15),r.rpc=t(12),r.roots=t(11),r.configure=u,u()},{10:10,11:11,12:12,15:15,16:16,17:17,9:9}],9:[function(t,n,i){n.exports=f;var r,u=t(15),s=u.LongBits,e=u.utf8;function h(t,n){return RangeError("index out of range: "+t.pos+" + "+(n||1)+" > "+t.len)}function f(t){this.buf=t,this.pos=0,this.len=t.length}function o(){return u.Buffer?function(t){return(f.create=function(t){return u.Buffer.isBuffer(t)?new r(t):a(t)})(t)}:a}var c,a="undefined"!=typeof Uint8Array?function(t){if(t instanceof Uint8Array||Array.isArray(t))return new f(t);throw Error("illegal buffer")}:function(t){if(Array.isArray(t))return new f(t);throw Error("illegal buffer")};function l(){var t=new s(0,0),n=0;if(!(4<this.len-this.pos)){for(;n<3;++n){if(this.pos>=this.len)throw h(this);if(t.lo=(t.lo|(127&this.buf[this.pos])<<7*n)>>>0,this.buf[this.pos++]<128)return t}return t.lo=(t.lo|(127&this.buf[this.pos++])<<7*n)>>>0,t}for(;n<4;++n)if(t.lo=(t.lo|(127&this.buf[this.pos])<<7*n)>>>0,this.buf[this.pos++]<128)return t;if(t.lo=(t.lo|(127&this.buf[this.pos])<<28)>>>0,t.hi=(t.hi|(127&this.buf[this.pos])>>4)>>>0,this.buf[this.pos++]<128)return t;if(n=0,4<this.len-this.pos){for(;n<5;++n)if(t.hi=(t.hi|(127&this.buf[this.pos])<<7*n+3)>>>0,this.buf[this.pos++]<128)return t}else for(;n<5;++n){if(this.pos>=this.len)throw h(this);if(t.hi=(t.hi|(127&this.buf[this.pos])<<7*n+3)>>>0,this.buf[this.pos++]<128)return t}throw Error("invalid varint encoding")}function v(t,n){return(t[n-4]|t[n-3]<<8|t[n-2]<<16|t[n-1]<<24)>>>0}function w(){if(this.pos+8>this.len)throw h(this,8);return new s(v(this.buf,this.pos+=4),v(this.buf,this.pos+=4))}f.create=o(),f.prototype.i=u.Array.prototype.subarray||u.Array.prototype.slice,f.prototype.uint32=(c=4294967295,function(){if(c=(127&this.buf[this.pos])>>>0,this.buf[this.pos++]<128)return c;if(c=(c|(127&this.buf[this.pos])<<7)>>>0,this.buf[this.pos++]<128)return c;if(c=(c|(127&this.buf[this.pos])<<14)>>>0,this.buf[this.pos++]<128)return c;if(c=(c|(127&this.buf[this.pos])<<21)>>>0,this.buf[this.pos++]<128)return c;if(c=(c|(15&this.buf[this.pos])<<28)>>>0,this.buf[this.pos++]<128)return c;if((this.pos+=5)>this.len)throw this.pos=this.len,h(this,10);return c}),f.prototype.int32=function(){return 0|this.uint32()},f.prototype.sint32=function(){var t=this.uint32();return t>>>1^-(1&t)|0},f.prototype.bool=function(){return 0!==this.uint32()},f.prototype.fixed32=function(){if(this.pos+4>this.len)throw h(this,4);return v(this.buf,this.pos+=4)},f.prototype.sfixed32=function(){if(this.pos+4>this.len)throw h(this,4);return 0|v(this.buf,this.pos+=4)},f.prototype.float=function(){if(this.pos+4>this.len)throw h(this,4);var t=u.float.readFloatLE(this.buf,this.pos);return this.pos+=4,t},f.prototype.double=function(){if(this.pos+8>this.len)throw h(this,4);var t=u.float.readDoubleLE(this.buf,this.pos);return this.pos+=8,t},f.prototype.bytes=function(){var t=this.uint32(),n=this.pos,i=this.pos+t;if(i>this.len)throw h(this,t);return this.pos+=t,Array.isArray(this.buf)?this.buf.slice(n,i):n===i?new this.buf.constructor(0):this.i.call(this.buf,n,i)},f.prototype.string=function(){var t=this.bytes();return e.read(t,0,t.length)},f.prototype.skip=function(t){if("number"==typeof t){if(this.pos+t>this.len)throw h(this,t);this.pos+=t}else do{if(this.pos>=this.len)throw h(this)}while(128&this.buf[this.pos++]);return this},f.prototype.skipType=function(t){switch(t){case 0:this.skip();break;case 1:this.skip(8);break;case 2:this.skip(this.uint32());break;case 3:for(;4!=(t=7&this.uint32());)this.skipType(t);break;case 5:this.skip(4);break;default:throw Error("invalid wire type "+t+" at offset "+this.pos)}return this},f.n=function(t){r=t,f.create=o(),r.n();var n=u.Long?"toLong":"toNumber";u.merge(f.prototype,{int64:function(){return l.call(this)[n](!1)},uint64:function(){return l.call(this)[n](!0)},sint64:function(){return l.call(this).zzDecode()[n](!1)},fixed64:function(){return w.call(this)[n](!0)},sfixed64:function(){return w.call(this)[n](!1)}})}},{15:15}],10:[function(t,n,i){n.exports=s;var r=t(9),u=((s.prototype=Object.create(r.prototype)).constructor=s,t(15));function s(t){r.call(this,t)}s.n=function(){u.Buffer&&(s.prototype.i=u.Buffer.prototype.slice)},s.prototype.string=function(){var t=this.uint32();return this.buf.utf8Slice?this.buf.utf8Slice(this.pos,this.pos=Math.min(this.pos+t,this.len)):this.buf.toString("utf-8",this.pos,this.pos=Math.min(this.pos+t,this.len))},s.n()},{15:15,9:9}],11:[function(t,n,i){n.exports={}},{}],12:[function(t,n,i){i.Service=t(13)},{13:13}],13:[function(t,n,i){n.exports=r;var h=t(15);function r(t,n,i){if("function"!=typeof t)throw TypeError("rpcImpl must be a function");h.EventEmitter.call(this),this.rpcImpl=t,this.requestDelimited=!!n,this.responseDelimited=!!i}((r.prototype=Object.create(h.EventEmitter.prototype)).constructor=r).prototype.rpcCall=function t(i,n,r,u,s){if(!u)throw TypeError("request must be specified");var e=this;if(!s)return h.asPromise(t,e,i,n,r,u);if(!e.rpcImpl)return setTimeout(function(){s(Error("already ended"))},0),g;try{return e.rpcImpl(i,n[e.requestDelimited?"encodeDelimited":"encode"](u).finish(),function(t,n){if(t)return e.emit("error",t,i),s(t);if(null===n)return e.end(!0),g;if(!(n instanceof r))try{n=r[e.responseDelimited?"decodeDelimited":"decode"](n)}catch(t){return e.emit("error",t,i),s(t)}return e.emit("data",n,i),s(null,n)})}catch(t){return e.emit("error",t,i),setTimeout(function(){s(t)},0),g}},r.prototype.end=function(t){return this.rpcImpl&&(t||this.rpcImpl(null,null,null),this.rpcImpl=null,this.emit("end").off()),this}},{15:15}],14:[function(t,n,i){n.exports=u;var r=t(15);function u(t,n){this.lo=t>>>0,this.hi=n>>>0}var s=u.zero=new u(0,0),e=(s.toNumber=function(){return 0},s.zzEncode=s.zzDecode=function(){return this},s.length=function(){return 1},u.zeroHash="\0\0\0\0\0\0\0\0",u.fromNumber=function(t){if(0===t)return s;var n=t<0,i=(t=n?-t:t)>>>0,t=(t-i)/4294967296>>>0;return n&&(t=~t>>>0,i=~i>>>0,4294967295<++i&&(i=0,4294967295<++t&&(t=0))),new u(i,t)},u.from=function(t){if("number"==typeof t)return u.fromNumber(t);if(r.isString(t)){if(!r.Long)return u.fromNumber(parseInt(t,10));t=r.Long.fromString(t)}return t.low||t.high?new u(t.low>>>0,t.high>>>0):s},u.prototype.toNumber=function(t){var n;return!t&&this.hi>>>31?(t=1+~this.lo>>>0,n=~this.hi>>>0,-(t+4294967296*(n=t?n:n+1>>>0))):this.lo+4294967296*this.hi},u.prototype.toLong=function(t){return r.Long?new r.Long(0|this.lo,0|this.hi,!!t):{low:0|this.lo,high:0|this.hi,unsigned:!!t}},String.prototype.charCodeAt);u.fromHash=function(t){return"\0\0\0\0\0\0\0\0"===t?s:new u((e.call(t,0)|e.call(t,1)<<8|e.call(t,2)<<16|e.call(t,3)<<24)>>>0,(e.call(t,4)|e.call(t,5)<<8|e.call(t,6)<<16|e.call(t,7)<<24)>>>0)},u.prototype.toHash=function(){return String.fromCharCode(255&this.lo,this.lo>>>8&255,this.lo>>>16&255,this.lo>>>24,255&this.hi,this.hi>>>8&255,this.hi>>>16&255,this.hi>>>24)},u.prototype.zzEncode=function(){var t=this.hi>>31;return this.hi=((this.hi<<1|this.lo>>>31)^t)>>>0,this.lo=(this.lo<<1^t)>>>0,this},u.prototype.zzDecode=function(){var t=-(1&this.lo);return this.lo=((this.lo>>>1|this.hi<<31)^t)>>>0,this.hi=(this.hi>>>1^t)>>>0,this},u.prototype.length=function(){var t=this.lo,n=(this.lo>>>28|this.hi<<4)>>>0,i=this.hi>>>24;return 0==i?0==n?t<16384?t<128?1:2:t<2097152?3:4:n<16384?n<128?5:6:n<2097152?7:8:i<128?9:10}},{15:15}],15:[function(t,n,i){var r=i;function u(t,n,i){for(var r=Object.keys(n),u=0;u<r.length;++u)t[r[u]]!==g&&i||(t[r[u]]=n[r[u]]);return t}function s(t){function i(t,n){if(!(this instanceof i))return new i(t,n);Object.defineProperty(this,"message",{get:function(){return t}}),Error.captureStackTrace?Error.captureStackTrace(this,i):Object.defineProperty(this,"stack",{value:Error().stack||""}),n&&u(this,n)}return(i.prototype=Object.create(Error.prototype)).constructor=i,Object.defineProperty(i.prototype,"name",{get:function(){return t}}),i.prototype.toString=function(){return this.name+": "+this.message},i}r.asPromise=t(1),r.base64=t(2),r.EventEmitter=t(3),r.float=t(4),r.inquire=t(5),r.utf8=t(7),r.pool=t(6),r.LongBits=t(14),r.isNode=!!("undefined"!=typeof global&&global&&global.process&&global.process.versions&&global.process.versions.node),r.global=r.isNode&&global||"undefined"!=typeof window&&window||"undefined"!=typeof self&&self||this,r.emptyArray=Object.freeze?Object.freeze([]):[],r.emptyObject=Object.freeze?Object.freeze({}):{},r.isInteger=Number.isInteger||function(t){return"number"==typeof t&&isFinite(t)&&Math.floor(t)===t},r.isString=function(t){return"string"==typeof t||t instanceof String},r.isObject=function(t){return t&&"object"==typeof t},r.isset=r.isSet=function(t,n){var i=t[n];return null!=i&&t.hasOwnProperty(n)&&("object"!=typeof i||0<(Array.isArray(i)?i:Object.keys(i)).length)},r.Buffer=function(){try{var t=r.inquire("buffer").Buffer;return t.prototype.utf8Write?t:null}catch(t){return null}}(),r.r=null,r.u=null,r.newBuffer=function(t){return"number"==typeof t?r.Buffer?r.u(t):new r.Array(t):r.Buffer?r.r(t):"undefined"==typeof Uint8Array?t:new Uint8Array(t)},r.Array="undefined"!=typeof Uint8Array?Uint8Array:Array,r.Long=r.global.dcodeIO&&r.global.dcodeIO.Long||r.global.Long||r.inquire("long"),r.key2Re=/^true|false|0|1$/,r.key32Re=/^-?(?:0|[1-9][0-9]*)$/,r.key64Re=/^(?:[\\x00-\\xff]{8}|-?(?:0|[1-9][0-9]*))$/,r.longToHash=function(t){return t?r.LongBits.from(t).toHash():r.LongBits.zeroHash},r.longFromHash=function(t,n){t=r.LongBits.fromHash(t);return r.Long?r.Long.fromBits(t.lo,t.hi,n):t.toNumber(!!n)},r.merge=u,r.lcFirst=function(t){return(t[0]||"").toLowerCase()+t.substring(1)},r.newError=s,r.ProtocolError=s("ProtocolError"),r.oneOfGetter=function(t){for(var i={},n=0;n<t.length;++n)i[t[n]]=1;return function(){for(var t=Object.keys(this),n=t.length-1;-1<n;--n)if(1===i[t[n]]&&this[t[n]]!==g&&null!==this[t[n]])return t[n]}},r.oneOfSetter=function(i){return function(t){for(var n=0;n<i.length;++n)i[n]!==t&&delete this[i[n]]}},r.toJSONOptions={longs:String,enums:String,bytes:String,json:!0},r.n=function(){var i=r.Buffer;i?(r.r=i.from!==Uint8Array.from&&i.from||function(t,n){return new i(t,n)},r.u=i.allocUnsafe||function(t){return new i(t)}):r.r=r.u=null}},{1:1,14:14,2:2,3:3,4:4,5:5,6:6,7:7}],16:[function(t,n,i){n.exports=a;var r,u=t(15),s=u.LongBits,e=u.base64,h=u.utf8;function f(t,n,i){this.fn=t,this.len=n,this.next=g,this.val=i}function o(){}function c(t){this.head=t.head,this.tail=t.tail,this.len=t.len,this.next=t.states}function a(){this.len=0,this.head=new f(o,0,0),this.tail=this.head,this.states=null}function l(){return u.Buffer?function(){return(a.create=function(){return new r})()}:function(){return new a}}function v(t,n,i){n[i]=255&t}function w(t,n){this.len=t,this.next=g,this.val=n}function y(t,n,i){for(;t.hi;)n[i++]=127&t.lo|128,t.lo=(t.lo>>>7|t.hi<<25)>>>0,t.hi>>>=7;for(;127<t.lo;)n[i++]=127&t.lo|128,t.lo=t.lo>>>7;n[i++]=t.lo}function b(t,n,i){n[i]=255&t,n[i+1]=t>>>8&255,n[i+2]=t>>>16&255,n[i+3]=t>>>24}a.create=l(),a.alloc=function(t){return new u.Array(t)},u.Array!==Array&&(a.alloc=u.pool(a.alloc,u.Array.prototype.subarray)),a.prototype.s=function(t,n,i){return this.tail=this.tail.next=new f(t,n,i),this.len+=n,this},(w.prototype=Object.create(f.prototype)).fn=function(t,n,i){for(;127<t;)n[i++]=127&t|128,t>>>=7;n[i]=t},a.prototype.uint32=function(t){return this.len+=(this.tail=this.tail.next=new w((t>>>=0)<128?1:t<16384?2:t<2097152?3:t<268435456?4:5,t)).len,this},a.prototype.int32=function(t){return t<0?this.s(y,10,s.fromNumber(t)):this.uint32(t)},a.prototype.sint32=function(t){return this.uint32((t<<1^t>>31)>>>0)},a.prototype.int64=a.prototype.uint64=function(t){t=s.from(t);return this.s(y,t.length(),t)},a.prototype.sint64=function(t){t=s.from(t).zzEncode();return this.s(y,t.length(),t)},a.prototype.bool=function(t){return this.s(v,1,t?1:0)},a.prototype.sfixed32=a.prototype.fixed32=function(t){return this.s(b,4,t>>>0)},a.prototype.sfixed64=a.prototype.fixed64=function(t){t=s.from(t);return this.s(b,4,t.lo).s(b,4,t.hi)},a.prototype.float=function(t){return this.s(u.float.writeFloatLE,4,t)},a.prototype.double=function(t){return this.s(u.float.writeDoubleLE,8,t)};var d=u.Array.prototype.set?function(t,n,i){n.set(t,i)}:function(t,n,i){for(var r=0;r<t.length;++r)n[i+r]=t[r]};a.prototype.bytes=function(t){var n,i=t.length>>>0;return i?(u.isString(t)&&(n=a.alloc(i=e.length(t)),e.decode(t,n,0),t=n),this.uint32(i).s(d,i,t)):this.s(v,1,0)},a.prototype.string=function(t){var n=h.length(t);return n?this.uint32(n).s(h.write,n,t):this.s(v,1,0)},a.prototype.fork=function(){return this.states=new c(this),this.head=this.tail=new f(o,0,0),this.len=0,this},a.prototype.reset=function(){return this.states?(this.head=this.states.head,this.tail=this.states.tail,this.len=this.states.len,this.states=this.states.next):(this.head=this.tail=new f(o,0,0),this.len=0),this},a.prototype.ldelim=function(){var t=this.head,n=this.tail,i=this.len;return this.reset().uint32(i),i&&(this.tail.next=t.next,this.tail=n,this.len+=i),this},a.prototype.finish=function(){for(var t=this.head.next,n=this.constructor.alloc(this.len),i=0;t;)t.fn(t.val,n,i),i+=t.len,t=t.next;return n},a.n=function(t){r=t,a.create=l(),r.n()}},{15:15}],17:[function(t,n,i){n.exports=s;var r=t(16),u=((s.prototype=Object.create(r.prototype)).constructor=s,t(15));function s(){r.call(this)}function e(t,n,i){t.length<40?u.utf8.write(t,n,i):n.utf8Write?n.utf8Write(t,i):n.write(t,i)}s.n=function(){s.alloc=u.u,s.writeBytesBuffer=u.Buffer&&u.Buffer.prototype instanceof Uint8Array&&"set"===u.Buffer.prototype.set.name?function(t,n,i){n.set(t,i)}:function(t,n,i){if(t.copy)t.copy(n,i,0,t.length);else for(var r=0;r<t.length;)n[i++]=t[r++]}},s.prototype.bytes=function(t){var n=(t=u.isString(t)?u.r(t,"base64"):t).length>>>0;return this.uint32(n),n&&this.s(s.writeBytesBuffer,n,t),this},s.prototype.string=function(t){var n=u.Buffer.byteLength(t);return this.uint32(n),n&&this.s(e,n,t),this},s.n()},{15:15,16:16}]},{},[8])}();
//# sourceMappingURL=protobuf.min.js.map
