/* App Layout */
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', sans-serif;
}

.app-header {
  text-align: center;
  padding: 2rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.app-header h1 {
  margin: 0 0 1rem 0;
  font-size: 2.5rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.app-header p {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

.app-main {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 2rem 1rem;
}

/* Voice Recorder Component */
.voice-recorder {
  background: rgba(255, 255, 255, 0.95);
  color: #333;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  max-width: 600px;
  width: 100%;
  backdrop-filter: blur(10px);
}

.recorder-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.mic-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 50px;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
  min-width: 200px;
}

.mic-button:hover:not(.disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

.mic-button.recording {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  animation: pulse 2s infinite;
}

.mic-button.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.mic-icon {
  font-size: 1.2rem;
}

.mic-icon.recording {
  animation: blink 1s infinite;
}

.clear-button {
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 25px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.clear-button:hover {
  background: #5a6268;
  transform: translateY(-1px);
}

/* Error and Status Messages */
.error-message {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  border-radius: 10px;
  padding: 1rem;
  margin: 1rem 0;
  text-align: center;
}

.success-message {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
  border-radius: 10px;
  padding: 1rem;
  margin: 1rem 0;
  text-align: center;
  font-weight: 500;
}

.retry-button {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 0.5rem 1rem;
  margin-top: 0.5rem;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.retry-button:hover {
  background: #c82333;
  transform: translateY(-1px);
}

/* Transcription Results */
.transcription-result {
  margin: 2rem 0;
}

.transcription-result h3 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.3rem;
}

.transcription-text {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 10px;
  padding: 1.5rem;
  font-size: 1.1rem;
  line-height: 1.6;
  color: #333;
  min-height: 60px;
  text-align: left;
  word-wrap: break-word;
}

/* Instructions */
.instructions {
  background: #e3f2fd;
  border-radius: 10px;
  padding: 1.5rem;
  margin-top: 2rem;
  text-align: left;
}

.instructions h4 {
  margin: 0 0 1rem 0;
  color: #1976d2;
  font-size: 1.1rem;
}

.instructions ul {
  margin: 0;
  padding-left: 1.5rem;
  color: #333;
}

.instructions li {
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

/* Animations */
@keyframes pulse {
  0% {
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
  }
  50% {
    box-shadow: 0 4px 25px rgba(255, 107, 107, 0.8);
  }
  100% {
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.3;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .app-header h1 {
    font-size: 2rem;
  }

  .app-header p {
    font-size: 1rem;
  }

  .voice-recorder {
    margin: 0 1rem;
    padding: 1.5rem;
  }

  .mic-button {
    min-width: 180px;
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
  }
}
