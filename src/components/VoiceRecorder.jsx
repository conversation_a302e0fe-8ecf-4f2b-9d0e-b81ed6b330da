import { useState, useRef, useEffect } from 'react'
import { pipeline } from '@xenova/transformers'

const VoiceRecorder = () => {
  const [isRecording, setIsRecording] = useState(false)
  const [isTranscribing, setIsTranscribing] = useState(false)
  const [transcription, setTranscription] = useState('')
  const [error, setError] = useState('')
  const [isModelLoading, setIsModelLoading] = useState(false)
  const [modelReady, setModelReady] = useState(false)
  
  const mediaRecorderRef = useRef(null)
  const audioContextRef = useRef(null)
  const analyserRef = useRef(null)
  const silenceTimeoutRef = useRef(null)
  const audioChunksRef = useRef([])
  const transcriptionPipelineRef = useRef(null)

  // Initialize the transcription model
  useEffect(() => {
    const initializeModel = async () => {
      try {
        setIsModelLoading(true)
        setError('🔄 Downloading transcription model... This may take 1-3 minutes on first use. The model will be cached for future use.')

        console.log('Starting to load Whisper model...')

        // Initialize the Whisper pipeline for automatic speech recognition
        // Using the base model which is smaller and faster to load
        transcriptionPipelineRef.current = await pipeline(
          'automatic-speech-recognition',
          'Xenova/whisper-base.en',
          {
            revision: 'main',
            quantized: true,
            progress_callback: (progress) => {
              console.log('Model loading progress:', progress)
              if (progress.status === 'downloading') {
                const percent = Math.round((progress.loaded / progress.total) * 100)
                setError(`📥 Downloading model: ${percent}% (${Math.round(progress.loaded / 1024 / 1024)}MB / ${Math.round(progress.total / 1024 / 1024)}MB)`)
              } else if (progress.status === 'loading') {
                setError('🔄 Loading model into memory...')
              }
            }
          }
        )

        console.log('Whisper model loaded successfully!')
        setError('')
        setIsModelLoading(false)
        setModelReady(true)
      } catch (err) {
        console.error('Error loading model:', err)
        setError('❌ Failed to load transcription model. Please check your internet connection and refresh the page.')
        setIsModelLoading(false)
      }
    }

    initializeModel()
  }, [])

  // Silence detection function
  const detectSilence = () => {
    if (!analyserRef.current) return

    const bufferLength = analyserRef.current.frequencyBinCount
    const dataArray = new Uint8Array(bufferLength)
    analyserRef.current.getByteFrequencyData(dataArray)

    // Calculate average volume
    const average = dataArray.reduce((sum, value) => sum + value, 0) / bufferLength
    const silenceThreshold = 10 // Adjust this value to change sensitivity

    if (average < silenceThreshold) {
      // Start silence timeout if not already started
      if (!silenceTimeoutRef.current) {
        silenceTimeoutRef.current = setTimeout(() => {
          stopRecording()
        }, 2000) // Stop after 2 seconds of silence
      }
    } else {
      // Clear silence timeout if there's sound
      if (silenceTimeoutRef.current) {
        clearTimeout(silenceTimeoutRef.current)
        silenceTimeoutRef.current = null
      }
    }

    // Continue monitoring if still recording
    if (isRecording) {
      requestAnimationFrame(detectSilence)
    }
  }

  const startRecording = async () => {
    try {
      setError('')
      setTranscription('')
      audioChunksRef.current = []

      // Request microphone access
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        } 
      })

      // Set up audio context for silence detection
      audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)()
      analyserRef.current = audioContextRef.current.createAnalyser()
      const source = audioContextRef.current.createMediaStreamSource(stream)
      source.connect(analyserRef.current)
      
      analyserRef.current.fftSize = 256
      analyserRef.current.smoothingTimeConstant = 0.8

      // Set up MediaRecorder
      mediaRecorderRef.current = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      })

      mediaRecorderRef.current.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data)
        }
      }

      mediaRecorderRef.current.onstop = async () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm;codecs=opus' })
        await transcribeAudio(audioBlob)
        
        // Clean up
        stream.getTracks().forEach(track => track.stop())
        if (audioContextRef.current) {
          audioContextRef.current.close()
        }
      }

      // Start recording
      mediaRecorderRef.current.start(100) // Collect data every 100ms
      setIsRecording(true)

      // Start silence detection
      detectSilence()

    } catch (err) {
      console.error('Error starting recording:', err)
      setError('Failed to access microphone. Please check permissions.')
    }
  }

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop()
      setIsRecording(false)
      
      // Clear silence timeout
      if (silenceTimeoutRef.current) {
        clearTimeout(silenceTimeoutRef.current)
        silenceTimeoutRef.current = null
      }
    }
  }

  const transcribeAudio = async (audioBlob) => {
    if (!transcriptionPipelineRef.current) {
      setError('Transcription model not loaded yet. Please wait and try again.')
      return
    }

    try {
      setIsTranscribing(true)
      setError('')

      // Convert blob to array buffer
      const arrayBuffer = await audioBlob.arrayBuffer()
      
      // Transcribe the audio
      const result = await transcriptionPipelineRef.current(arrayBuffer)
      
      setTranscription(result.text || 'No speech detected')
      setIsTranscribing(false)
      
    } catch (err) {
      console.error('Error transcribing audio:', err)
      setError('Failed to transcribe audio. Please try again.')
      setIsTranscribing(false)
    }
  }

  const clearTranscription = () => {
    setTranscription('')
    setError('')
  }

  const retryModelLoad = () => {
    setIsModelLoading(true)
    setModelReady(false)
    setError('')

    // Re-run the model initialization
    const initializeModel = async () => {
      try {
        setError('🔄 Retrying model download...')

        transcriptionPipelineRef.current = await pipeline(
          'automatic-speech-recognition',
          'Xenova/whisper-base.en',
          {
            revision: 'main',
            quantized: true,
            progress_callback: (progress) => {
              if (progress.status === 'downloading') {
                const percent = Math.round((progress.loaded / progress.total) * 100)
                setError(`📥 Downloading model: ${percent}% (${Math.round(progress.loaded / 1024 / 1024)}MB / ${Math.round(progress.total / 1024 / 1024)}MB)`)
              } else if (progress.status === 'loading') {
                setError('🔄 Loading model into memory...')
              }
            }
          }
        )

        setError('')
        setIsModelLoading(false)
        setModelReady(true)
      } catch (err) {
        console.error('Error loading model:', err)
        setError('❌ Failed to load transcription model. Please check your internet connection.')
        setIsModelLoading(false)
      }
    }

    initializeModel()
  }

  return (
    <div className="voice-recorder">
      <div className="recorder-controls">
        <button
          className={`mic-button ${isRecording ? 'recording' : ''} ${isModelLoading ? 'disabled' : ''}`}
          onClick={isRecording ? stopRecording : startRecording}
          disabled={isModelLoading || isTranscribing}
        >
          {isRecording ? (
            <>
              <span className="mic-icon recording">🔴</span>
              <span>Recording... (Auto-stops on silence)</span>
            </>
          ) : isTranscribing ? (
            <>
              <span className="mic-icon">⏳</span>
              <span>Transcribing...</span>
            </>
          ) : isModelLoading ? (
            <>
              <span className="mic-icon">⏳</span>
              <span>Loading Model...</span>
            </>
          ) : (
            <>
              <span className="mic-icon">🎤</span>
              <span>Start Recording</span>
            </>
          )}
        </button>

        {transcription && (
          <button className="clear-button" onClick={clearTranscription}>
            Clear
          </button>
        )}
      </div>

      {error && (
        <div className="error-message">
          {error}
          {!isModelLoading && !modelReady && (
            <button className="retry-button" onClick={retryModelLoad}>
              🔄 Retry Loading Model
            </button>
          )}
        </div>
      )}

      {modelReady && !error && (
        <div className="success-message">
          ✅ Transcription model ready! You can now start recording.
        </div>
      )}

      {transcription && (
        <div className="transcription-result">
          <h3>Transcription:</h3>
          <div className="transcription-text">
            {transcription}
          </div>
        </div>
      )}

      <div className="instructions">
        <h4>How to use:</h4>
        <ul>
          <li>Click the microphone button to start recording</li>
          <li>Speak clearly into your microphone</li>
          <li>Recording will automatically stop after 2 seconds of silence</li>
          <li>Your speech will be transcribed and displayed below</li>
        </ul>
      </div>
    </div>
  )
}

export default VoiceRecorder
