import { useState, useRef, useEffect } from 'react'

// We'll try both approaches: Web Speech API (instant) and Whisper (offline)
let whisperPipeline = null

// Try to import transformers, but don't fail if it doesn't work
const loadWhisper = async () => {
  try {
    const { pipeline, env } = await import('@xenova/transformers')
    env.allowRemoteModels = true
    env.allowLocalModels = false
    return pipeline
  } catch (error) {
    console.warn('Whisper not available, using Web Speech API only:', error)
    return null
  }
}

const VoiceRecorder = () => {
  const [isRecording, setIsRecording] = useState(false)
  const [isTranscribing, setIsTranscribing] = useState(false)
  const [transcription, setTranscription] = useState('')
  const [error, setError] = useState('')
  const [isModelLoading, setIsModelLoading] = useState(false)
  const [modelReady, setModelReady] = useState(false)
  const [useWebSpeech, setUseWebSpeech] = useState(true)

  const mediaRecorderRef = useRef(null)
  const audioContextRef = useRef(null)
  const analyserRef = useRef(null)
  const silenceTimeoutRef = useRef(null)
  const audioChunksRef = useRef([])
  const transcriptionPipelineRef = useRef(null)
  const recognitionRef = useRef(null)

  // Initialize the transcription model
  useEffect(() => {
    const initializeModel = async () => {
      try {
        setIsModelLoading(true)
        setError('🔄 Initializing speech recognition...')

        console.log('Starting to load Whisper model...')

        // Check if Web Speech API is available (built into most browsers)
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
          console.log('Web Speech API available - using for instant transcription')
          setUseWebSpeech(true)
          setError('')
          setIsModelLoading(false)
          setModelReady(true)
          return
        }

        // Fallback to Whisper if Web Speech API is not available
        console.log('Web Speech API not available, trying Whisper...')
        setUseWebSpeech(false)

        const pipelineFunc = await loadWhisper()
        if (!pipelineFunc) {
          throw new Error('Neither Web Speech API nor Whisper is available')
        }

        // Initialize the Whisper pipeline for automatic speech recognition
        transcriptionPipelineRef.current = await pipelineFunc(
          'automatic-speech-recognition',
          'Xenova/whisper-tiny.en',
          {
            quantized: true,
            progress_callback: (progress) => {
              console.log('Model loading progress:', progress)
              if (progress.status === 'downloading') {
                const percent = Math.round((progress.loaded / progress.total) * 100)
                setError(`📥 Downloading model: ${percent}% (${Math.round(progress.loaded / 1024 / 1024)}MB / ${Math.round(progress.total / 1024 / 1024)}MB)`)
              } else if (progress.status === 'loading') {
                setError('🔄 Loading model into memory...')
              }
            }
          }
        )

        console.log('Whisper model loaded successfully!')
        setError('')
        setIsModelLoading(false)
        setModelReady(true)
      } catch (err) {
        console.error('Error loading model:', err)
        setError('❌ Speech recognition not available. Please use a modern browser like Chrome, Edge, or Safari.')
        setIsModelLoading(false)
      }
    }

    initializeModel()
  }, [])

  // Silence detection function
  const detectSilence = () => {
    if (!analyserRef.current) return

    const bufferLength = analyserRef.current.frequencyBinCount
    const dataArray = new Uint8Array(bufferLength)
    analyserRef.current.getByteFrequencyData(dataArray)

    // Calculate average volume
    const average = dataArray.reduce((sum, value) => sum + value, 0) / bufferLength
    const silenceThreshold = 10 // Adjust this value to change sensitivity

    if (average < silenceThreshold) {
      // Start silence timeout if not already started
      if (!silenceTimeoutRef.current) {
        silenceTimeoutRef.current = setTimeout(() => {
          stopRecording()
        }, 2000) // Stop after 2 seconds of silence
      }
    } else {
      // Clear silence timeout if there's sound
      if (silenceTimeoutRef.current) {
        clearTimeout(silenceTimeoutRef.current)
        silenceTimeoutRef.current = null
      }
    }

    // Continue monitoring if still recording
    if (isRecording) {
      requestAnimationFrame(detectSilence)
    }
  }

  const startRecording = async () => {
    try {
      setError('')
      setTranscription('')

      if (useWebSpeech) {
        // Use Web Speech API for instant transcription
        startWebSpeechRecording()
      } else {
        // Use traditional recording + Whisper transcription
        startTraditionalRecording()
      }
    } catch (err) {
      console.error('Error starting recording:', err)
      setError('Failed to start recording. Please check microphone permissions.')
    }
  }

  const startWebSpeechRecording = () => {
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
    recognitionRef.current = new SpeechRecognition()

    recognitionRef.current.continuous = true
    recognitionRef.current.interimResults = true
    recognitionRef.current.lang = 'en-US'

    recognitionRef.current.onstart = () => {
      setIsRecording(true)
      console.log('Web Speech API started')
    }

    recognitionRef.current.onresult = (event) => {
      let finalTranscript = ''
      let interimTranscript = ''

      for (let i = event.resultIndex; i < event.results.length; i++) {
        const transcript = event.results[i][0].transcript
        if (event.results[i].isFinal) {
          finalTranscript += transcript
        } else {
          interimTranscript += transcript
        }
      }

      setTranscription(finalTranscript + interimTranscript)
    }

    recognitionRef.current.onerror = (event) => {
      console.error('Speech recognition error:', event.error)
      setError(`Speech recognition error: ${event.error}`)
      setIsRecording(false)
    }

    recognitionRef.current.onend = () => {
      setIsRecording(false)
      console.log('Web Speech API ended')
    }

    // Start silence detection for auto-stop
    navigator.mediaDevices.getUserMedia({ audio: true })
      .then(stream => {
        audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)()
        analyserRef.current = audioContextRef.current.createAnalyser()
        const source = audioContextRef.current.createMediaStreamSource(stream)
        source.connect(analyserRef.current)

        analyserRef.current.fftSize = 256
        analyserRef.current.smoothingTimeConstant = 0.8

        detectSilence()

        // Store stream reference for cleanup
        recognitionRef.current.stream = stream
      })
      .catch(err => {
        console.error('Error accessing microphone for silence detection:', err)
      })

    recognitionRef.current.start()
  }

  const startTraditionalRecording = async () => {
    audioChunksRef.current = []

    // Request microphone access
    const stream = await navigator.mediaDevices.getUserMedia({
      audio: {
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true
      }
    })

    // Set up audio context for silence detection
    audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)()
    analyserRef.current = audioContextRef.current.createAnalyser()
    const source = audioContextRef.current.createMediaStreamSource(stream)
    source.connect(analyserRef.current)

    analyserRef.current.fftSize = 256
    analyserRef.current.smoothingTimeConstant = 0.8

    // Set up MediaRecorder
    mediaRecorderRef.current = new MediaRecorder(stream, {
      mimeType: 'audio/webm;codecs=opus'
    })

    mediaRecorderRef.current.ondataavailable = (event) => {
      if (event.data.size > 0) {
        audioChunksRef.current.push(event.data)
      }
    }

    mediaRecorderRef.current.onstop = async () => {
      const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm;codecs=opus' })
      await transcribeAudio(audioBlob)

      // Clean up
      stream.getTracks().forEach(track => track.stop())
      if (audioContextRef.current) {
        audioContextRef.current.close()
      }
    }

    // Start recording
    mediaRecorderRef.current.start(100) // Collect data every 100ms
    setIsRecording(true)

    // Start silence detection
    detectSilence()
  }

  const stopRecording = () => {
    if (useWebSpeech && recognitionRef.current) {
      recognitionRef.current.stop()

      // Clean up microphone stream
      if (recognitionRef.current.stream) {
        recognitionRef.current.stream.getTracks().forEach(track => track.stop())
      }

      if (audioContextRef.current) {
        audioContextRef.current.close()
      }
    } else if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop()
    }

    setIsRecording(false)

    // Clear silence timeout
    if (silenceTimeoutRef.current) {
      clearTimeout(silenceTimeoutRef.current)
      silenceTimeoutRef.current = null
    }
  }

  const transcribeAudio = async (audioBlob) => {
    if (!transcriptionPipelineRef.current) {
      setError('Transcription model not loaded yet. Please wait and try again.')
      return
    }

    try {
      setIsTranscribing(true)
      setError('')

      // Convert blob to array buffer
      const arrayBuffer = await audioBlob.arrayBuffer()
      
      // Transcribe the audio
      const result = await transcriptionPipelineRef.current(arrayBuffer)
      
      setTranscription(result.text || 'No speech detected')
      setIsTranscribing(false)
      
    } catch (err) {
      console.error('Error transcribing audio:', err)
      setError('Failed to transcribe audio. Please try again.')
      setIsTranscribing(false)
    }
  }

  const clearTranscription = () => {
    setTranscription('')
    setError('')
  }

  const retryModelLoad = () => {
    setIsModelLoading(true)
    setModelReady(false)
    setError('')

    // Re-run the model initialization
    const initializeModel = async () => {
      try {
        setError('🔄 Retrying model download...')

        transcriptionPipelineRef.current = await pipeline(
          'automatic-speech-recognition',
          'Xenova/whisper-tiny.en',
          {
            quantized: true,
            progress_callback: (progress) => {
              if (progress.status === 'downloading') {
                const percent = Math.round((progress.loaded / progress.total) * 100)
                setError(`📥 Downloading model: ${percent}% (${Math.round(progress.loaded / 1024 / 1024)}MB / ${Math.round(progress.total / 1024 / 1024)}MB)`)
              } else if (progress.status === 'loading') {
                setError('🔄 Loading model into memory...')
              }
            }
          }
        )

        setError('')
        setIsModelLoading(false)
        setModelReady(true)
      } catch (err) {
        console.error('Error loading model:', err)
        setError('❌ Failed to load transcription model. Please check your internet connection.')
        setIsModelLoading(false)
      }
    }

    initializeModel()
  }

  return (
    <div className="voice-recorder">
      <div className="recorder-controls">
        <button
          className={`mic-button ${isRecording ? 'recording' : ''} ${isModelLoading ? 'disabled' : ''}`}
          onClick={isRecording ? stopRecording : startRecording}
          disabled={isModelLoading || isTranscribing}
        >
          {isRecording ? (
            <>
              <span className="mic-icon recording">🔴</span>
              <span>Recording... (Auto-stops on silence)</span>
            </>
          ) : isTranscribing ? (
            <>
              <span className="mic-icon">⏳</span>
              <span>Transcribing...</span>
            </>
          ) : isModelLoading ? (
            <>
              <span className="mic-icon">⏳</span>
              <span>Loading Model...</span>
            </>
          ) : (
            <>
              <span className="mic-icon">🎤</span>
              <span>Start Recording</span>
            </>
          )}
        </button>

        {transcription && (
          <button className="clear-button" onClick={clearTranscription}>
            Clear
          </button>
        )}
      </div>

      {error && (
        <div className="error-message">
          {error}
          {!isModelLoading && !modelReady && (
            <button className="retry-button" onClick={retryModelLoad}>
              🔄 Retry Loading Model
            </button>
          )}
        </div>
      )}

      {modelReady && !error && (
        <div className="success-message">
          ✅ Transcription model ready! You can now start recording.
        </div>
      )}

      {transcription && (
        <div className="transcription-result">
          <h3>Transcription:</h3>
          <div className="transcription-text">
            {transcription}
          </div>
        </div>
      )}

      <div className="instructions">
        <h4>How to use:</h4>
        <ul>
          <li>Click the microphone button to start recording</li>
          <li>Speak clearly into your microphone</li>
          <li>Recording will automatically stop after 2 seconds of silence</li>
          <li>Your speech will be transcribed and displayed below</li>
        </ul>
      </div>
    </div>
  )
}

export default VoiceRecorder
